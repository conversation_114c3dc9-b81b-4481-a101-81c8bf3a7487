{"name": "sm2-api-server", "version": "1.0.0", "description": "SM2加密传输API服务器", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "pm2": "pm2 start server.js --name sm2-api", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["sm2", "encryption", "api", "loadrunner"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2", "sm-crypto": "^0.3.11"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}