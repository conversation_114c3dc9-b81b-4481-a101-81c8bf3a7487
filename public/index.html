[O<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SM2加密传输接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 15px;
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .key-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            white-space: pre-wrap;
        }
        
        .result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            white-space: pre-wrap;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .endpoint-box {
            background: #2d3748;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .method-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .post { background: #28a745; color: white; }
        .get { background: #007bff; color: white; }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SM2加密传输接口测试</h1>
        
        <div class="section">
            <h2>🔑 SM2密钥对生成</h2>
            <button onclick="generateKeyPair()">生成新的SM2密钥对</button>
            <button onclick="loadTestKeys()">使用测试密钥对</button>
            
            <div class="form-group">
                <label>公钥 (Public Key):</label>
                <div class="key-display" id="publicKey">点击生成密钥对...</div>
            </div>
            
            <div class="form-group">
                <label>私钥 (Private Key):</label>
                <div class="key-display" id="privateKey">点击生成密钥对...</div>
            </div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h2>📤 加密测试</h2>
                <div class="form-group">
                    <label>明文数据:</label>
                    <textarea id="plaintext" rows="4" placeholder="输入要加密的数据...">Hello, this is a test message for SM2 encryption!</textarea>
                </div>
                <button onclick="encryptData()">加密数据</button>
                <div class="result" id="encryptResult"></div>
            </div>
            
            <div class="section">
                <h2>📥 解密测试</h2>
                <div class="form-group">
                    <label>密文数据:</label>
                    <textarea id="ciphertext" rows="4" placeholder="输入要解密的密文..."></textarea>
                </div>
                <button onclick="decryptData()">解密数据</button>
                <div class="result" id="decryptResult"></div>
            </div>
        </div>
        
        <div class="section">
            <h2>🌐 API接口测试</h2>
            <div class="api-info">
                <h3>可用的API端点:</h3>
                <div class="endpoint-box">
                    <span class="method-tag post">POST</span>/api/encrypt
                    <br>请求体: {"data": "要加密的数据", "publicKey": "公钥"}
                </div>
                <div class="endpoint-box">
                    <span class="method-tag post">POST</span>/api/decrypt  
                    <br>请求体: {"encryptedData": "加密数据", "privateKey": "私钥"}
                </div>
                <div class="endpoint-box">
                    <span class="method-tag get">GET</span>/api/generateKeys
                    <br>返回: {"publicKey": "公钥", "privateKey": "私钥"}
                </div>
            </div>
            
            <div class="form-group">
                <label>API端点:</label>
                <select id="apiEndpoint">
                    <option value="/api/encrypt">POST /api/encrypt</option>
                    <option value="/api/decrypt">POST /api/decrypt</option>
                    <option value="/api/generateKeys">GET /api/generateKeys</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>请求数据 (JSON):</label>
                <textarea id="apiRequestData" rows="4" placeholder='{"data": "test message", "publicKey": "..."}'></textarea>
            </div>
            
            <button onclick="testAPI()">测试API</button>
            <div class="result" id="apiResult"></div>
        </div>
        
        <div class="section">
            <h2>📋 LoadRunner测试示例</h2>
            <div class="api-info">
                <h3>LoadRunner C语言测试代码示例:</h3>
                <div class="key-display">
Action()
{
    // SM2加密API测试
    web_add_header("Content-Type", "application/json");
    
    web_custom_request("SM2_Encrypt",
        "URL=http://localhost:8080/api/encrypt",
        "Method=POST",
        "Body={\"data\":\"LoadRunner Test Message\",\"publicKey\":\"04...your_public_key...\"}",
        "Snapshot=t1.inf",
        LAST);
        
    // 检查响应
    web_reg_find("Text=encryptedData",
        "SaveCount=encrypt_count",
        LAST);
        
    if (atoi(lr_eval_string("{encrypt_count}")) > 0) {
        lr_output_message("SM2加密成功");
    } else {
        lr_error_message("SM2加密失败");
    }
    
    return 0;
}
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟的SM2算法实现（简化版，实际项目中应使用专业的SM2库）
        class SimpleSM2 {
            static generateKeyPair() {
                // 这里使用模拟的密钥对，实际应用中需要使用真正的SM2算法
                const privateKey = this.generateRandomHex(64);
                const publicKey = '04' + this.generateRandomHex(128);
                return { privateKey, publicKey };
            }
            
            static generateRandomHex(length) {
                let result = '';
                const characters = '0123456789ABCDEF';
                for (let i = 0; i < length; i++) {
                    result += characters.charAt(Math.floor(Math.random() * characters.length));
                }
                return result;
            }
            
            static encrypt(data, publicKey) {
                // 模拟加密过程
                const encrypted = btoa(data + '|' + Date.now() + '|' + Math.random());
                return {
                    c1: this.generateRandomHex(128),
                    c2: encrypted,
                    c3: this.generateRandomHex(64)
                };
            }
            
            static decrypt(encryptedData, privateKey) {
                try {
                    // 模拟解密过程
                    const decrypted = atob(encryptedData.c2);
                    return decrypted.split('|')[0];
                } catch (e) {
                    throw new Error('解密失败');
                }
            }
        }
        
        let currentKeyPair = null;
        
        function generateKeyPair() {
            currentKeyPair = SimpleSM2.generateKeyPair();
            document.getElementById('publicKey').textContent = currentKeyPair.publicKey;
            document.getElementById('privateKey').textContent = currentKeyPair.privateKey;
            updateApiRequestData();
        }
        
        function loadTestKeys() {
            currentKeyPair = {
                privateKey: 'A1B2C3D4E5F6789012345678901234567890123456789012345678901234567890',
                publicKey: '04B2C3D4E5F6789012345678901234567890123456789012345678901234567890A1B2C3D4E5F6789012345678901234567890123456789012345678901234567890'
            };
            document.getElementById('publicKey').textContent = currentKeyPair.publicKey;
            document.getElementById('privateKey').textContent = currentKeyPair.privateKey;
            updateApiRequestData();
        }
        
        function updateApiRequestData() {
            if (currentKeyPair) {
                const requestData = {
                    data: "test message",
                    publicKey: currentKeyPair.publicKey
                };
                document.getElementById('apiRequestData').value = JSON.stringify(requestData, null, 2);
            }
        }
        
        function encryptData() {
            const plaintext = document.getElementById('plaintext').value;
            const resultDiv = document.getElementById('encryptResult');
            
            if (!currentKeyPair) {
                resultDiv.innerHTML = '<div class="error">请先生成密钥对</div>';
                return;
            }
            
            if (!plaintext.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入要加密的数据</div>';
                return;
            }
            
            try {
                const encrypted = SimpleSM2.encrypt(plaintext, currentKeyPair.publicKey);
                const result = {
                    success: true,
                    encryptedData: encrypted,
                    originalLength: plaintext.length,
                    timestamp: new Date().toISOString()
                };
                
                resultDiv.innerHTML = JSON.stringify(result, null, 2);
                
                // 自动填充解密输入框
                document.getElementById('ciphertext').value = JSON.stringify(encrypted);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">加密失败: ${error.message}</div>`;
            }
        }
        
        function decryptData() {
            const ciphertext = document.getElementById('ciphertext').value;
            const resultDiv = document.getElementById('decryptResult');
            
            if (!currentKeyPair) {
                resultDiv.innerHTML = '<div class="error">请先生成密钥对</div>';
                return;
            }
            
            if (!ciphertext.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入要解密的密文</div>';
                return;
            }
            
            try {
                const encryptedData = JSON.parse(ciphertext);
                const decrypted = SimpleSM2.decrypt(encryptedData, currentKeyPair.privateKey);
                const result = {
                    success: true,
                    decryptedData: decrypted,
                    timestamp: new Date().toISOString()
                };
                
                resultDiv.innerHTML = JSON.stringify(result, null, 2);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">解密失败: ${error.message}</div>`;
            }
        }
        
        function testAPI() {
            const endpoint = document.getElementById('apiEndpoint').value;
            const requestData = document.getElementById('apiRequestData').value;
            const resultDiv = document.getElementById('apiResult');
            
            // 模拟API调用
            setTimeout(() => {
                try {
                    let response;
                    
                    if (endpoint === '/api/generateKeys') {
                        const keyPair = SimpleSM2.generateKeyPair();
                        response = {
                            success: true,
                            publicKey: keyPair.publicKey,
                            privateKey: keyPair.privateKey,
                            timestamp: new Date().toISOString()
                        };
                    } else if (endpoint === '/api/encrypt') {
                        const data = JSON.parse(requestData);
                        const encrypted = SimpleSM2.encrypt(data.data, data.publicKey);
                        response = {
                            success: true,
                            encryptedData: encrypted,
                            timestamp: new Date().toISOString()
                        };
                    } else if (endpoint === '/api/decrypt') {
                        const data = JSON.parse(requestData);
                        const decrypted = SimpleSM2.decrypt(data.encryptedData, data.privateKey);
                        response = {
                            success: true,
                            decryptedData: decrypted,
                            timestamp: new Date().toISOString()
                        };
                    }
                    
                    resultDiv.innerHTML = `<strong>API响应:</strong>\n${JSON.stringify(response, null, 2)}`;
                    
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">API调用失败: ${error.message}</div>`;
                }
            }, 500); // 模拟网络延迟
        }
        
        // 页面加载时自动生成测试密钥对
        window.onload = function() {
            loadTestKeys();
        };
        
        // 监听端点变化，自动更新请求数据示例
        document.getElementById('apiEndpoint').addEventListener('change', function() {
            const endpoint = this.value;
            let exampleData = '';
            
            if (endpoint === '/api/encrypt') {
                exampleData = JSON.stringify({
                    data: "test message for encryption",
                    publicKey: currentKeyPair ? currentKeyPair.publicKey : "04..."
                }, null, 2);
            } else if (endpoint === '/api/decrypt') {
                exampleData = JSON.stringify({
                    encryptedData: {
                        c1: "C1部分...",
                        c2: "C2部分...",
                        c3: "C3部分..."
                    },
                    privateKey: currentKeyPair ? currentKeyPair.privateKey : "私钥..."
                }, null, 2);
            } else if (endpoint === '/api/generateKeys') {
                exampleData = '无需请求体 (GET请求)';
            }
            
            document.getElementById('apiRequestData').value = exampleData;
        });
    </script>
</body>
</html>
