const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { sm2 } = require('sm-crypto');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 8080;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(express.static('public'));

// 生成SM2密钥对
app.get('/api/generateKeys', (req, res) => {
    try {
        const keyPair = sm2.generateKeyPairHex();
        res.json({
            success: true,
            publicKey: keyPair.publicKey,
            privateKey: keyPair.privateKey,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// SM2加密接口
app.post('/api/encrypt', (req, res) => {
    try {
        const { data, publicKey } = req.body;
        
        if (!data || !publicKey) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数：data 和 publicKey'
            });
        }

        const encryptedData = sm2.doEncrypt(data, publicKey, 1);
        
        res.json({
            success: true,
            encryptedData: encryptedData,
            originalLength: data.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// SM2解密接口
app.post('/api/decrypt', (req, res) => {
    try {
        const { encryptedData, privateKey } = req.body;
        
        if (!encryptedData || !privateKey) {
            return res.status(400).json({
                success: false,
                error: '缺少必要参数：encryptedData 和 privateKey'
            });
        }

        const decryptedData = sm2.doDecrypt(encryptedData, privateKey, 1);
        
        res.json({
            success: true,
            decryptedData: decryptedData,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 健康检查接口
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});

// 首页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`SM2加密服务已启动: http://localhost:${PORT}`);
    console.log(`API文档: http://localhost:${PORT}/api/health`);
});
